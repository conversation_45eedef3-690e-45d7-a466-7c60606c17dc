/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export { ApiError } from './core/ApiError';
export { CancelablePromise, CancelError } from './core/CancelablePromise';
export { OpenAPI } from './core/OpenAPI';
export type { OpenAPIConfig } from './core/OpenAPI';

export type { AddAddressCommand } from './models/AddAddressCommand';
export type { AddAddressResponse } from './models/AddAddressResponse';
export type { AddReviewReplyCommand } from './models/AddReviewReplyCommand';
export type { AddTicketReplyCommand } from './models/AddTicketReplyCommand';
export type { ApiResponseOfAddAddressResponse } from './models/ApiResponseOfAddAddressResponse';
export type { ApiResponseOfCacheStatistics } from './models/ApiResponseOfCacheStatistics';
export type { ApiResponseOfCancelDeactivationResponse } from './models/ApiResponseOfCancelDeactivationResponse';
export type { ApiResponseOfChangePasswordResponse } from './models/ApiResponseOfChangePasswordResponse';
export type { ApiResponseOfCommandsCheckoutResultDto } from './models/ApiResponseOfCommandsCheckoutResultDto';
export type { ApiResponseOfConfirmEmailResponse } from './models/ApiResponseOfConfirmEmailResponse';
export type { ApiResponseOfCreateUserResponse } from './models/ApiResponseOfCreateUserResponse';
export type { ApiResponseOfDeactivateAccountResponse } from './models/ApiResponseOfDeactivateAccountResponse';
export type { ApiResponseOfDeleteAddressResponse } from './models/ApiResponseOfDeleteAddressResponse';
export type { ApiResponseOfDictionaryOfSystemStringAndExternalServicesApiMetricsReport } from './models/ApiResponseOfDictionaryOfSystemStringAndExternalServicesApiMetricsReport';
export type { ApiResponseOfDictionaryOfSystemStringAndSystemBoolean } from './models/ApiResponseOfDictionaryOfSystemStringAndSystemBoolean';
export type { ApiResponseOfDTOsAITranslationStatusDto } from './models/ApiResponseOfDTOsAITranslationStatusDto';
export type { ApiResponseOfDTOsCartDto } from './models/ApiResponseOfDTOsCartDto';
export type { ApiResponseOfDTOsCartItemDto } from './models/ApiResponseOfDTOsCartItemDto';
export type { ApiResponseOfDTOsInventoryItemDto } from './models/ApiResponseOfDTOsInventoryItemDto';
export type { ApiResponseOfDTOsInvoiceDto } from './models/ApiResponseOfDTOsInvoiceDto';
export type { ApiResponseOfDTOsPermissionGroupDto } from './models/ApiResponseOfDTOsPermissionGroupDto';
export type { ApiResponseOfDTOsReturnRequestDto } from './models/ApiResponseOfDTOsReturnRequestDto';
export type { ApiResponseOfDTOsReviewDto } from './models/ApiResponseOfDTOsReviewDto';
export type { ApiResponseOfDTOsReviewReplyDto } from './models/ApiResponseOfDTOsReviewReplyDto';
export type { ApiResponseOfDTOsRoleDto } from './models/ApiResponseOfDTOsRoleDto';
export type { ApiResponseOfExternalServicesApiMetricsReport } from './models/ApiResponseOfExternalServicesApiMetricsReport';
export type { ApiResponseOfExternalTranslationQuota } from './models/ApiResponseOfExternalTranslationQuota';
export type { ApiResponseOfForgotPasswordResponse } from './models/ApiResponseOfForgotPasswordResponse';
export type { ApiResponseOfGetInventoryAlertsInventoryAlertsDto } from './models/ApiResponseOfGetInventoryAlertsInventoryAlertsDto';
export type { ApiResponseOfGetInventoryOverviewInventoryOverviewDto } from './models/ApiResponseOfGetInventoryOverviewInventoryOverviewDto';
export type { ApiResponseOfGetInventoryTurnoverRateInventoryTurnoverRateDto } from './models/ApiResponseOfGetInventoryTurnoverRateInventoryTurnoverRateDto';
export type { ApiResponseOfGetProductRatingStatsProductRatingStatsDto } from './models/ApiResponseOfGetProductRatingStatsProductRatingStatsDto';
export type { ApiResponseOfGetSalesOverviewSalesOverviewDto } from './models/ApiResponseOfGetSalesOverviewSalesOverviewDto';
export type { ApiResponseOfGetSalesTrendSalesTrendDto } from './models/ApiResponseOfGetSalesTrendSalesTrendDto';
export type { ApiResponseOfGetUserAddressesResponse } from './models/ApiResponseOfGetUserAddressesResponse';
export type { ApiResponseOfGetUserGrowthStatsUserGrowthStatsDto } from './models/ApiResponseOfGetUserGrowthStatsUserGrowthStatsDto';
export type { ApiResponseOfGetUserPurchaseBehaviorUserPurchaseBehaviorDto } from './models/ApiResponseOfGetUserPurchaseBehaviorUserPurchaseBehaviorDto';
export type { ApiResponseOfGetUserResponse } from './models/ApiResponseOfGetUserResponse';
export type { ApiResponseOfGetUserSegmentationUserSegmentationDto } from './models/ApiResponseOfGetUserSegmentationUserSegmentationDto';
export type { ApiResponseOfIEnumerableOfExternalLanguageInfo } from './models/ApiResponseOfIEnumerableOfExternalLanguageInfo';
export type { ApiResponseOfIEnumerableOfMarketingContentDto } from './models/ApiResponseOfIEnumerableOfMarketingContentDto';
export type { ApiResponseOfIEnumerableOfMarketingCouponDto } from './models/ApiResponseOfIEnumerableOfMarketingCouponDto';
export type { ApiResponseOfIEnumerableOfMarketingPromotionDto } from './models/ApiResponseOfIEnumerableOfMarketingPromotionDto';
export type { ApiResponseOfIEnumerableOfPaymentsPaymentDto } from './models/ApiResponseOfIEnumerableOfPaymentsPaymentDto';
export type { ApiResponseOfIEnumerableOfPaymentsPaymentMethodDto } from './models/ApiResponseOfIEnumerableOfPaymentsPaymentMethodDto';
export type { ApiResponseOfIEnumerableOfSystemString } from './models/ApiResponseOfIEnumerableOfSystemString';
export type { ApiResponseOfListOfCacheKeyStatistics } from './models/ApiResponseOfListOfCacheKeyStatistics';
export type { ApiResponseOfListOfDTOsMenuDto } from './models/ApiResponseOfListOfDTOsMenuDto';
export type { ApiResponseOfListOfDTOsPermissionGroupDto } from './models/ApiResponseOfListOfDTOsPermissionGroupDto';
export type { ApiResponseOfListOfDTOsPermissionMetadataDto } from './models/ApiResponseOfListOfDTOsPermissionMetadataDto';
export type { ApiResponseOfListOfDTOsReturnRequestDto } from './models/ApiResponseOfListOfDTOsReturnRequestDto';
export type { ApiResponseOfListOfDTOsReviewDto } from './models/ApiResponseOfListOfDTOsReviewDto';
export type { ApiResponseOfListOfDTOsRoleDto } from './models/ApiResponseOfListOfDTOsRoleDto';
export type { ApiResponseOfListOfDTOsSearchSuggestionDto } from './models/ApiResponseOfListOfDTOsSearchSuggestionDto';
export type { ApiResponseOfListOfExternalLanguageInfo } from './models/ApiResponseOfListOfExternalLanguageInfo';
export type { ApiResponseOfListOfMarketingCouponDto } from './models/ApiResponseOfListOfMarketingCouponDto';
export type { ApiResponseOfListOfMerchantsMerchantDto } from './models/ApiResponseOfListOfMerchantsMerchantDto';
export type { ApiResponseOfListOfProductsProductAttributeDto } from './models/ApiResponseOfListOfProductsProductAttributeDto';
export type { ApiResponseOfListOfProductsProductCategoryDto } from './models/ApiResponseOfListOfProductsProductCategoryDto';
export type { ApiResponseOfListOfProductsProductImageDto } from './models/ApiResponseOfListOfProductsProductImageDto';
export type { ApiResponseOfListOfProductsProductVariantDto } from './models/ApiResponseOfListOfProductsProductVariantDto';
export type { ApiResponseOfListOfQueriesRoleInheritanceDto } from './models/ApiResponseOfListOfQueriesRoleInheritanceDto';
export type { ApiResponseOfListOfShippingAreaDto } from './models/ApiResponseOfListOfShippingAreaDto';
export type { ApiResponseOfListOfShippingOriginDto } from './models/ApiResponseOfListOfShippingOriginDto';
export type { ApiResponseOfListOfShippingProviderDto } from './models/ApiResponseOfListOfShippingProviderDto';
export type { ApiResponseOfListOfShippingRateDto } from './models/ApiResponseOfListOfShippingRateDto';
export type { ApiResponseOfListOfShippingShipmentDto } from './models/ApiResponseOfListOfShippingShipmentDto';
export type { ApiResponseOfListOfSystemString } from './models/ApiResponseOfListOfSystemString';
export type { ApiResponseOfListOfTaxProductCategoryTaxRateDto } from './models/ApiResponseOfListOfTaxProductCategoryTaxRateDto';
export type { ApiResponseOfListOfTicketsBusinessReferenceDto } from './models/ApiResponseOfListOfTicketsBusinessReferenceDto';
export type { ApiResponseOfListOfTicketsTicketCategoryDto } from './models/ApiResponseOfListOfTicketsTicketCategoryDto';
export type { ApiResponseOfLoginResponse } from './models/ApiResponseOfLoginResponse';
export type { ApiResponseOfMarketingContentDto } from './models/ApiResponseOfMarketingContentDto';
export type { ApiResponseOfMarketingCouponDto } from './models/ApiResponseOfMarketingCouponDto';
export type { ApiResponseOfMarketingPromotionDto } from './models/ApiResponseOfMarketingPromotionDto';
export type { ApiResponseOfMarketingPromotionSearchResult } from './models/ApiResponseOfMarketingPromotionSearchResult';
export type { ApiResponseOfMarketingValidateCouponResult } from './models/ApiResponseOfMarketingValidateCouponResult';
export type { ApiResponseOfMerchantsMerchantDto } from './models/ApiResponseOfMerchantsMerchantDto';
export type { ApiResponseOfOrdersOrderDto } from './models/ApiResponseOfOrdersOrderDto';
export type { ApiResponseOfOrdersOrderWithChildrenDto } from './models/ApiResponseOfOrdersOrderWithChildrenDto';
export type { ApiResponseOfOrdersUserOrdersDto } from './models/ApiResponseOfOrdersUserOrdersDto';
export type { ApiResponseOfPaginatedListOfGetUserResponse } from './models/ApiResponseOfPaginatedListOfGetUserResponse';
export type { ApiResponseOfPaginatedListOfMarketingContentDto } from './models/ApiResponseOfPaginatedListOfMarketingContentDto';
export type { ApiResponseOfPaymentsInitiatePaymentResultDto } from './models/ApiResponseOfPaymentsInitiatePaymentResultDto';
export type { ApiResponseOfPaymentsPaymentDto } from './models/ApiResponseOfPaymentsPaymentDto';
export type { ApiResponseOfPaymentsPaymentMethodDto } from './models/ApiResponseOfPaymentsPaymentMethodDto';
export type { ApiResponseOfPaymentsWebhookResultDto } from './models/ApiResponseOfPaymentsWebhookResultDto';
export type { ApiResponseOfPermissionsUserPermissionsDto } from './models/ApiResponseOfPermissionsUserPermissionsDto';
export type { ApiResponseOfProductsProductAttributeDto } from './models/ApiResponseOfProductsProductAttributeDto';
export type { ApiResponseOfProductsProductCategoryDto } from './models/ApiResponseOfProductsProductCategoryDto';
export type { ApiResponseOfProductsProductDto } from './models/ApiResponseOfProductsProductDto';
export type { ApiResponseOfProductsProductTranslationsDto } from './models/ApiResponseOfProductsProductTranslationsDto';
export type { ApiResponseOfProductsTopSellingProductsDto } from './models/ApiResponseOfProductsTopSellingProductsDto';
export type { ApiResponseOfRefreshTokenResponse } from './models/ApiResponseOfRefreshTokenResponse';
export type { ApiResponseOfRegisterUserResponse } from './models/ApiResponseOfRegisterUserResponse';
export type { ApiResponseOfResendEmailConfirmationResponse } from './models/ApiResponseOfResendEmailConfirmationResponse';
export type { ApiResponseOfResetPasswordResponse } from './models/ApiResponseOfResetPasswordResponse';
export type { ApiResponseOfResetPasswordWithCodeResponse } from './models/ApiResponseOfResetPasswordWithCodeResponse';
export type { ApiResponseOfShippingAreaDto } from './models/ApiResponseOfShippingAreaDto';
export type { ApiResponseOfShippingShipmentDto } from './models/ApiResponseOfShippingShipmentDto';
export type { ApiResponseOfSuspendUserResponse } from './models/ApiResponseOfSuspendUserResponse';
export type { ApiResponseOfSystemBoolean } from './models/ApiResponseOfSystemBoolean';
export type { ApiResponseOfSystemGuid } from './models/ApiResponseOfSystemGuid';
export type { ApiResponseOfSystemInt32 } from './models/ApiResponseOfSystemInt32';
export type { ApiResponseOfSystemObject } from './models/ApiResponseOfSystemObject';
export type { ApiResponseOfSystemString } from './models/ApiResponseOfSystemString';
export type { ApiResponseOfTaxCalculationResultDto } from './models/ApiResponseOfTaxCalculationResultDto';
export type { ApiResponseOfTaxProductCategoryTaxRateDto } from './models/ApiResponseOfTaxProductCategoryTaxRateDto';
export type { ApiResponseOfTaxRateDto } from './models/ApiResponseOfTaxRateDto';
export type { ApiResponseOfTicketsTicketCategoryDto } from './models/ApiResponseOfTicketsTicketCategoryDto';
export type { ApiResponseOfTicketsTicketDto } from './models/ApiResponseOfTicketsTicketDto';
export type { ApiResponseOfTicketsTicketReplyDto } from './models/ApiResponseOfTicketsTicketReplyDto';
export type { ApiResponseOfUpdateAddressResponse } from './models/ApiResponseOfUpdateAddressResponse';
export type { ApiResponseOfUpdateUserResponse } from './models/ApiResponseOfUpdateUserResponse';
export type { ApiResponseOfUpdateUserStatusResponse } from './models/ApiResponseOfUpdateUserStatusResponse';
export type { ApiResponseOfUserRolesDto } from './models/ApiResponseOfUserRolesDto';
export type { ApiResponseOfV1CreateSettingResultDto } from './models/ApiResponseOfV1CreateSettingResultDto';
export type { ApiResponseOfV1RequiresProductApprovalDto } from './models/ApiResponseOfV1RequiresProductApprovalDto';
export type { ApiResponseOfVerifyEmailWithCodeResponse } from './models/ApiResponseOfVerifyEmailWithCodeResponse';
export type { ApproveProductCommand } from './models/ApproveProductCommand';
export type { ApproveReturnCommand } from './models/ApproveReturnCommand';
export type { CacheKeyStatistics } from './models/CacheKeyStatistics';
export type { CacheStatistics } from './models/CacheStatistics';
export type { CalculateShippingRatesCommand } from './models/CalculateShippingRatesCommand';
export type { CancelDeactivationCommand } from './models/CancelDeactivationCommand';
export type { CancelDeactivationResponse } from './models/CancelDeactivationResponse';
export type { CartsAddItemToCartCommand } from './models/CartsAddItemToCartCommand';
export type { CartsCheckoutCartCommand } from './models/CartsCheckoutCartCommand';
export type { CartsUpdateCartItemCommand } from './models/CartsUpdateCartItemCommand';
export type { ChangePasswordCommand } from './models/ChangePasswordCommand';
export type { ChangePasswordResponse } from './models/ChangePasswordResponse';
export type { ClearCacheCommand } from './models/ClearCacheCommand';
export type { CommandsCheckoutResultDto } from './models/CommandsCheckoutResultDto';
export { CommandsInventoryUpdateType } from './models/CommandsInventoryUpdateType';
export type { ConfirmEmailResponse } from './models/ConfirmEmailResponse';
export type { CreateMerchantCommand } from './models/CreateMerchantCommand';
export type { CreateReviewCommand } from './models/CreateReviewCommand';
export type { CreateShipmentCommand } from './models/CreateShipmentCommand';
export type { CreateShippingAreaCommand } from './models/CreateShippingAreaCommand';
export type { CreateSystemSettingCommand } from './models/CreateSystemSettingCommand';
export type { CreateTicketCategoryCommand } from './models/CreateTicketCategoryCommand';
export type { CreateTicketCommand } from './models/CreateTicketCommand';
export type { CreateUserCommand } from './models/CreateUserCommand';
export type { CreateUserResponse } from './models/CreateUserResponse';
export type { DeactivateAccountCommand } from './models/DeactivateAccountCommand';
export type { DeactivateAccountResponse } from './models/DeactivateAccountResponse';
export type { DeleteAddressResponse } from './models/DeleteAddressResponse';
export type { DTOsAITranslationStatusDto } from './models/DTOsAITranslationStatusDto';
export type { DTOsCartDto } from './models/DTOsCartDto';
export type { DTOsCartItemDetailDto } from './models/DTOsCartItemDetailDto';
export type { DTOsCartItemDto } from './models/DTOsCartItemDto';
export type { DTOsInventoryItemDto } from './models/DTOsInventoryItemDto';
export type { DTOsInvoiceDto } from './models/DTOsInvoiceDto';
export type { DTOsInvoiceItemDto } from './models/DTOsInvoiceItemDto';
export type { DTOsMenuDto } from './models/DTOsMenuDto';
export type { DTOsMenuItemDto } from './models/DTOsMenuItemDto';
export type { DTOsMenuItemMetaDto } from './models/DTOsMenuItemMetaDto';
export type { DTOsMenuMetaDto } from './models/DTOsMenuMetaDto';
export type { DTOsPermissionGroupDto } from './models/DTOsPermissionGroupDto';
export type { DTOsPermissionMetadataDto } from './models/DTOsPermissionMetadataDto';
export type { DTOsReturnItemDto } from './models/DTOsReturnItemDto';
export type { DTOsReturnRequestDto } from './models/DTOsReturnRequestDto';
export type { DTOsReturnsAddressDto } from './models/DTOsReturnsAddressDto';
export type { DTOsReviewDto } from './models/DTOsReviewDto';
export type { DTOsReviewImageDto } from './models/DTOsReviewImageDto';
export type { DTOsReviewReplyDto } from './models/DTOsReviewReplyDto';
export type { DTOsRoleDto } from './models/DTOsRoleDto';
export type { DTOsSearchSuggestionDto } from './models/DTOsSearchSuggestionDto';
export { DTOsSuggestionType } from './models/DTOsSuggestionType';
export type { DTOsSystemSettingDto } from './models/DTOsSystemSettingDto';
export { EnumsTimeGranularity } from './models/EnumsTimeGranularity';
export type { ExternalLanguageInfo } from './models/ExternalLanguageInfo';
export type { ExternalServicesApiCall } from './models/ExternalServicesApiCall';
export type { ExternalServicesApiMetricsReport } from './models/ExternalServicesApiMetricsReport';
export type { ExternalTranslationQuota } from './models/ExternalTranslationQuota';
export type { ForgotPasswordCommand } from './models/ForgotPasswordCommand';
export type { ForgotPasswordResponse } from './models/ForgotPasswordResponse';
export type { GetInventoryAlertsCategoryAlertDto } from './models/GetInventoryAlertsCategoryAlertDto';
export type { GetInventoryAlertsInventoryAlertsDto } from './models/GetInventoryAlertsInventoryAlertsDto';
export type { GetInventoryAlertsLowStockItemDto } from './models/GetInventoryAlertsLowStockItemDto';
export type { GetInventoryOverviewCategoryInventoryValueDto } from './models/GetInventoryOverviewCategoryInventoryValueDto';
export type { GetInventoryOverviewInventoryOverviewDto } from './models/GetInventoryOverviewInventoryOverviewDto';
export type { GetInventoryTurnoverRateCategoryTurnoverRateDto } from './models/GetInventoryTurnoverRateCategoryTurnoverRateDto';
export type { GetInventoryTurnoverRateInventoryTurnoverRateDto } from './models/GetInventoryTurnoverRateInventoryTurnoverRateDto';
export type { GetInventoryTurnoverRateTurnoverRateTrendDto } from './models/GetInventoryTurnoverRateTurnoverRateTrendDto';
export type { GetProductRatingStatsProductRatingStatsDto } from './models/GetProductRatingStatsProductRatingStatsDto';
export type { GetSalesOverviewSalesOverviewDto } from './models/GetSalesOverviewSalesOverviewDto';
export type { GetSalesTrendSalesTrendDataPoint } from './models/GetSalesTrendSalesTrendDataPoint';
export type { GetSalesTrendSalesTrendDto } from './models/GetSalesTrendSalesTrendDto';
export type { GetUserAddressesResponse } from './models/GetUserAddressesResponse';
export type { GetUserAddressesUserAddressDto } from './models/GetUserAddressesUserAddressDto';
export type { GetUserGrowthStatsUserGrowthDataPoint } from './models/GetUserGrowthStatsUserGrowthDataPoint';
export type { GetUserGrowthStatsUserGrowthStatsDto } from './models/GetUserGrowthStatsUserGrowthStatsDto';
export type { GetUserPurchaseBehaviorOrderFrequencyDistributionItem } from './models/GetUserPurchaseBehaviorOrderFrequencyDistributionItem';
export type { GetUserPurchaseBehaviorPopularCategoryItem } from './models/GetUserPurchaseBehaviorPopularCategoryItem';
export type { GetUserPurchaseBehaviorPurchaseTimeDistributionItem } from './models/GetUserPurchaseBehaviorPurchaseTimeDistributionItem';
export type { GetUserPurchaseBehaviorUserPurchaseBehaviorDto } from './models/GetUserPurchaseBehaviorUserPurchaseBehaviorDto';
export type { GetUserResponse } from './models/GetUserResponse';
export type { GetUserSegmentationAgeGroupSegmentItem } from './models/GetUserSegmentationAgeGroupSegmentItem';
export type { GetUserSegmentationPurchaseFrequencySegmentItem } from './models/GetUserSegmentationPurchaseFrequencySegmentItem';
export type { GetUserSegmentationRegionSegmentItem } from './models/GetUserSegmentationRegionSegmentItem';
export type { GetUserSegmentationSpendingAmountSegmentItem } from './models/GetUserSegmentationSpendingAmountSegmentItem';
export type { GetUserSegmentationUserSegmentationDto } from './models/GetUserSegmentationUserSegmentationDto';
export type { InitiateRefundForReturnCommand } from './models/InitiateRefundForReturnCommand';
export type { InventoryCancelInventoryReservationCommand } from './models/InventoryCancelInventoryReservationCommand';
export type { InventoryConfirmInventoryReservationCommand } from './models/InventoryConfirmInventoryReservationCommand';
export type { InventoryCreateInventoryItemCommand } from './models/InventoryCreateInventoryItemCommand';
export type { InventoryReserveInventoryCommand } from './models/InventoryReserveInventoryCommand';
export type { InventoryUpdateInventoryQuantityCommand } from './models/InventoryUpdateInventoryQuantityCommand';
export type { InvoicesGenerateInvoiceCommand } from './models/InvoicesGenerateInvoiceCommand';
export { InvoiceStatus } from './models/InvoiceStatus';
export type { LoginCommand } from './models/LoginCommand';
export type { LoginResponse } from './models/LoginResponse';
export type { MarketingClaimCouponCommand } from './models/MarketingClaimCouponCommand';
export type { MarketingContentDto } from './models/MarketingContentDto';
export { MarketingContentStatus } from './models/MarketingContentStatus';
export { MarketingContentType } from './models/MarketingContentType';
export type { MarketingCouponDto } from './models/MarketingCouponDto';
export { MarketingCouponStatus } from './models/MarketingCouponStatus';
export type { MarketingCouponTranslationsDto } from './models/MarketingCouponTranslationsDto';
export type { MarketingCreateCouponCommand } from './models/MarketingCreateCouponCommand';
export type { MarketingCreateMarketingContentCommand } from './models/MarketingCreateMarketingContentCommand';
export type { MarketingCreatePromotionCommand } from './models/MarketingCreatePromotionCommand';
export type { MarketingPromotionDto } from './models/MarketingPromotionDto';
export type { MarketingPromotionItemDto } from './models/MarketingPromotionItemDto';
export { MarketingPromotionItemType } from './models/MarketingPromotionItemType';
export { MarketingPromotionScopeType } from './models/MarketingPromotionScopeType';
export type { MarketingPromotionSearchResult } from './models/MarketingPromotionSearchResult';
export { MarketingPromotionStatus } from './models/MarketingPromotionStatus';
export type { MarketingUpdateCouponCommand } from './models/MarketingUpdateCouponCommand';
export type { MarketingUpdateMarketingContentCommand } from './models/MarketingUpdateMarketingContentCommand';
export type { MarketingUpdateMarketingContentStatusCommand } from './models/MarketingUpdateMarketingContentStatusCommand';
export type { MarketingUpdatePromotionCommand } from './models/MarketingUpdatePromotionCommand';
export type { MarketingValidateCouponQuery } from './models/MarketingValidateCouponQuery';
export type { MarketingValidateCouponResult } from './models/MarketingValidateCouponResult';
export type { MarkReturnItemsAsReceivedCommand } from './models/MarkReturnItemsAsReceivedCommand';
export type { MerchantsMerchantDto } from './models/MerchantsMerchantDto';
export { MerchantStatus } from './models/MerchantStatus';
export type { ModelsApiResponse } from './models/ModelsApiResponse';
export type { MvcProblemDetails } from './models/MvcProblemDetails';
export type { OrdersCreateOrderCommand } from './models/OrdersCreateOrderCommand';
export type { OrdersOrderDto } from './models/OrdersOrderDto';
export type { OrdersOrderItemDto } from './models/OrdersOrderItemDto';
export type { OrdersOrderWithChildrenDto } from './models/OrdersOrderWithChildrenDto';
export type { OrdersUserOrdersDto } from './models/OrdersUserOrdersDto';
export type { PaginatedListOfGetUserResponse } from './models/PaginatedListOfGetUserResponse';
export type { PaginatedListOfMarketingContentDto } from './models/PaginatedListOfMarketingContentDto';
export type { PaymentsInitiatePaymentDto } from './models/PaymentsInitiatePaymentDto';
export type { PaymentsInitiatePaymentResultDto } from './models/PaymentsInitiatePaymentResultDto';
export type { PaymentsPaymentDto } from './models/PaymentsPaymentDto';
export type { PaymentsPaymentMethodDto } from './models/PaymentsPaymentMethodDto';
export type { PaymentsWebhookResultDto } from './models/PaymentsWebhookResultDto';
export type { PermissionGroupsCreatePermissionGroupCommand } from './models/PermissionGroupsCreatePermissionGroupCommand';
export type { PermissionGroupsUpdatePermissionGroupCommand } from './models/PermissionGroupsUpdatePermissionGroupCommand';
export type { PermissionsPermissionCategoryDto } from './models/PermissionsPermissionCategoryDto';
export type { PermissionsPermissionDto } from './models/PermissionsPermissionDto';
export type { PermissionsRolePermissionsDto } from './models/PermissionsRolePermissionsDto';
export type { PermissionsUpdateRolePermissionsCommand } from './models/PermissionsUpdateRolePermissionsCommand';
export type { PermissionsUserPermissionsDto } from './models/PermissionsUserPermissionsDto';
export type { ProductsAddAttributeOptionCommand } from './models/ProductsAddAttributeOptionCommand';
export type { ProductsAddProductImageCommand } from './models/ProductsAddProductImageCommand';
export type { ProductsAddProductVariantCommand } from './models/ProductsAddProductVariantCommand';
export type { ProductsAttributeOptionData } from './models/ProductsAttributeOptionData';
export type { ProductsCategoryTranslationsDto } from './models/ProductsCategoryTranslationsDto';
export type { ProductsCreateAttributeCommand } from './models/ProductsCreateAttributeCommand';
export type { ProductsCreateCategoryCommand } from './models/ProductsCreateCategoryCommand';
export type { ProductsCreateProductCommand } from './models/ProductsCreateProductCommand';
export type { ProductsMoveCategoryCommand } from './models/ProductsMoveCategoryCommand';
export { ProductSortOption } from './models/ProductSortOption';
export type { ProductsProductAttributeDto } from './models/ProductsProductAttributeDto';
export type { ProductsProductAttributeOptionDto } from './models/ProductsProductAttributeOptionDto';
export type { ProductsProductCategoryDto } from './models/ProductsProductCategoryDto';
export type { ProductsProductDto } from './models/ProductsProductDto';
export type { ProductsProductImageData } from './models/ProductsProductImageData';
export type { ProductsProductImageDto } from './models/ProductsProductImageDto';
export type { ProductsProductTranslationsDto } from './models/ProductsProductTranslationsDto';
export type { ProductsProductVariantData } from './models/ProductsProductVariantData';
export type { ProductsProductVariantDto } from './models/ProductsProductVariantDto';
export { ProductsTaxCategory } from './models/ProductsTaxCategory';
export type { ProductsTopSellingProductItem } from './models/ProductsTopSellingProductItem';
export type { ProductsTopSellingProductsDto } from './models/ProductsTopSellingProductsDto';
export type { ProductsUpdateAttributeCommand } from './models/ProductsUpdateAttributeCommand';
export type { ProductsUpdateAttributeOptionCommand } from './models/ProductsUpdateAttributeOptionCommand';
export type { ProductsUpdateCategoryCommand } from './models/ProductsUpdateCategoryCommand';
export type { ProductsUpdateProductCommand } from './models/ProductsUpdateProductCommand';
export type { ProductsUpdateProductImageCommand } from './models/ProductsUpdateProductImageCommand';
export type { ProductsUpdateProductVariantCommand } from './models/ProductsUpdateProductVariantCommand';
export type { QueriesRoleInheritanceDto } from './models/QueriesRoleInheritanceDto';
export type { RefreshTokenCommand } from './models/RefreshTokenCommand';
export type { RefreshTokenResponse } from './models/RefreshTokenResponse';
export type { RegisterUserCommand } from './models/RegisterUserCommand';
export type { RegisterUserResponse } from './models/RegisterUserResponse';
export type { RejectProductCommand } from './models/RejectProductCommand';
export type { RejectReturnCommand } from './models/RejectReturnCommand';
export type { RequestReturnCommand } from './models/RequestReturnCommand';
export type { RequestReturnReturnItemCommand } from './models/RequestReturnReturnItemCommand';
export type { ResendEmailConfirmationCommand } from './models/ResendEmailConfirmationCommand';
export type { ResendEmailConfirmationResponse } from './models/ResendEmailConfirmationResponse';
export type { ResetPasswordCommand } from './models/ResetPasswordCommand';
export type { ResetPasswordResponse } from './models/ResetPasswordResponse';
export type { ResetPasswordWithCodeCommand } from './models/ResetPasswordWithCodeCommand';
export type { ResetPasswordWithCodeResponse } from './models/ResetPasswordWithCodeResponse';
export type { RoleInheritanceAddInheritanceCommand } from './models/RoleInheritanceAddInheritanceCommand';
export type { RolesCreateRoleCommand } from './models/RolesCreateRoleCommand';
export type { RolesUpdateRoleCommand } from './models/RolesUpdateRoleCommand';
export { SettingsNotificationStatus } from './models/SettingsNotificationStatus';
export type { ShippingAddressDto } from './models/ShippingAddressDto';
export type { ShippingAreaDto } from './models/ShippingAreaDto';
export type { ShippingLabelDto } from './models/ShippingLabelDto';
export type { ShippingOriginDto } from './models/ShippingOriginDto';
export type { ShippingParcelDto } from './models/ShippingParcelDto';
export type { ShippingProviderDto } from './models/ShippingProviderDto';
export type { ShippingRateDto } from './models/ShippingRateDto';
export type { ShippingShipmentDto } from './models/ShippingShipmentDto';
export type { ShippingTrackingEventDto } from './models/ShippingTrackingEventDto';
export type { ShippingTrackingInfoDto } from './models/ShippingTrackingInfoDto';
export type { SuspendUserCommand } from './models/SuspendUserCommand';
export type { SuspendUserResponse } from './models/SuspendUserResponse';
export type { TaxAddProductCategoryTaxRateCommand } from './models/TaxAddProductCategoryTaxRateCommand';
export type { TaxCalculateTaxCommand } from './models/TaxCalculateTaxCommand';
export type { TaxCalculationItemDto } from './models/TaxCalculationItemDto';
export type { TaxCalculationResultDto } from './models/TaxCalculationResultDto';
export type { TaxLineItemDto } from './models/TaxLineItemDto';
export type { TaxProductCategoryTaxRateDto } from './models/TaxProductCategoryTaxRateDto';
export type { TaxRateDto } from './models/TaxRateDto';
export type { TicketsBusinessReferenceDto } from './models/TicketsBusinessReferenceDto';
export type { TicketsTicketCategoryDto } from './models/TicketsTicketCategoryDto';
export type { TicketsTicketDto } from './models/TicketsTicketDto';
export type { TicketsTicketReplyDto } from './models/TicketsTicketReplyDto';
export type { UpdateAddressCommand } from './models/UpdateAddressCommand';
export type { UpdateAddressResponse } from './models/UpdateAddressResponse';
export type { UpdateCategoryTranslationsCommand } from './models/UpdateCategoryTranslationsCommand';
export type { UpdateCouponTranslationsCommand } from './models/UpdateCouponTranslationsCommand';
export type { UpdateMerchantCommand } from './models/UpdateMerchantCommand';
export type { UpdateProductTranslationsCommand } from './models/UpdateProductTranslationsCommand';
export type { UpdateReviewCommand } from './models/UpdateReviewCommand';
export type { UpdateReviewStatusCommand } from './models/UpdateReviewStatusCommand';
export type { UpdateShipmentStatusCommand } from './models/UpdateShipmentStatusCommand';
export type { UpdateShippingAreaCommand } from './models/UpdateShippingAreaCommand';
export type { UpdateSystemSettingCommand } from './models/UpdateSystemSettingCommand';
export type { UpdateTicketStatusCommand } from './models/UpdateTicketStatusCommand';
export type { UpdateUserCommand } from './models/UpdateUserCommand';
export type { UpdateUserResponse } from './models/UpdateUserResponse';
export type { UpdateUserStatusCommand } from './models/UpdateUserStatusCommand';
export type { UpdateUserStatusResponse } from './models/UpdateUserStatusResponse';
export type { UserRolesDto } from './models/UserRolesDto';
export type { UserRolesUpdateUserRolesCommand } from './models/UserRolesUpdateUserRolesCommand';
export type { V1CreateSettingResultDto } from './models/V1CreateSettingResultDto';
export type { V1LogoutRequest } from './models/V1LogoutRequest';
export type { V1RequiresProductApprovalDto } from './models/V1RequiresProductApprovalDto';
export type { V1SendAnnouncementRequest } from './models/V1SendAnnouncementRequest';
export type { ValueObjectsAddress } from './models/ValueObjectsAddress';
export type { ValueObjectsAddressVo } from './models/ValueObjectsAddressVo';
export type { ValueObjectsDateRange } from './models/ValueObjectsDateRange';
export { ValueObjectsDiscountType } from './models/ValueObjectsDiscountType';
export type { ValueObjectsMoney } from './models/ValueObjectsMoney';
export { ValueObjectsOrderStatus } from './models/ValueObjectsOrderStatus';
export { ValueObjectsPromotionType } from './models/ValueObjectsPromotionType';
export { ValueObjectsReturnReason } from './models/ValueObjectsReturnReason';
export { ValueObjectsReturnStatus } from './models/ValueObjectsReturnStatus';
export { ValueObjectsShipmentStatus } from './models/ValueObjectsShipmentStatus';
export { ValueObjectsTicketBusinessType } from './models/ValueObjectsTicketBusinessType';
export { ValueObjectsTicketPriority } from './models/ValueObjectsTicketPriority';
export { ValueObjectsTicketStatus } from './models/ValueObjectsTicketStatus';
export type { VerifyEmailWithCodeCommand } from './models/VerifyEmailWithCodeCommand';
export type { VerifyEmailWithCodeResponse } from './models/VerifyEmailWithCodeResponse';

export { AccountsApiService } from './services/AccountsApiService';
export { ApiMetricsService } from './services/ApiMetricsService';
export { AttributesApiService } from './services/AttributesApiService';
export { CacheManagementService } from './services/CacheManagementService';
export { CartApiService } from './services/CartApiService';
export { CategoriesApiService } from './services/CategoriesApiService';
export { CouponsApiService } from './services/CouponsApiService';
export { HealthCheckApiService } from './services/HealthCheckApiService';
export { InventoryApiService } from './services/InventoryApiService';
export { InvoicesApiService } from './services/InvoicesApiService';
export { MarketingContentsApiService } from './services/MarketingContentsApiService';
export { MenuApiService } from './services/MenuApiService';
export { MerchantsApiService } from './services/MerchantsApiService';
export { NotificationsService } from './services/NotificationsService';
export { OrdersApiService } from './services/OrdersApiService';
export { PaymentsApiService } from './services/PaymentsApiService';
export { PermissionGroupsApiService } from './services/PermissionGroupsApiService';
export { PermissionsApiService } from './services/PermissionsApiService';
export { ProductsApiService } from './services/ProductsApiService';
export { PromotionsApiService } from './services/PromotionsApiService';
export { ReportsApiService } from './services/ReportsApiService';
export { ReturnsApiService } from './services/ReturnsApiService';
export { ReviewsApiService } from './services/ReviewsApiService';
export { RoleInheritanceApiService } from './services/RoleInheritanceApiService';
export { RolesApiService } from './services/RolesApiService';
export { SearchApiService } from './services/SearchApiService';
export { ShippingApiService } from './services/ShippingApiService';
export { ShippingAreaApiService } from './services/ShippingAreaApiService';
export { ShippoWebhookApiService } from './services/ShippoWebhookApiService';
export { ShipStationWebhookApiService } from './services/ShipStationWebhookApiService';
export { SocialLoginApiService } from './services/SocialLoginApiService';
export { SystemConfigApiService } from './services/SystemConfigApiService';
export { TaxApiService } from './services/TaxApiService';
export { TicketsApiService } from './services/TicketsApiService';
export { TranslationsApiService } from './services/TranslationsApiService';
export { UserAddressesApiService } from './services/UserAddressesApiService';
export { UserCouponsApiService } from './services/UserCouponsApiService';
export { UserRolesApiService } from './services/UserRolesApiService';
export { UsersApiService } from './services/UsersApiService';

/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ApiResponseOfCancelDeactivationResponse } from '../models/ApiResponseOfCancelDeactivationResponse';
import type { ApiResponseOfChangePasswordResponse } from '../models/ApiResponseOfChangePasswordResponse';
import type { ApiResponseOfConfirmEmailResponse } from '../models/ApiResponseOfConfirmEmailResponse';
import type { ApiResponseOfDeactivateAccountResponse } from '../models/ApiResponseOfDeactivateAccountResponse';
import type { ApiResponseOfForgotPasswordResponse } from '../models/ApiResponseOfForgotPasswordResponse';
import type { ApiResponseOfLoginResponse } from '../models/ApiResponseOfLoginResponse';
import type { ApiResponseOfRefreshTokenResponse } from '../models/ApiResponseOfRefreshTokenResponse';
import type { ApiResponseOfRegisterUserResponse } from '../models/ApiResponseOfRegisterUserResponse';
import type { ApiResponseOfResendEmailConfirmationResponse } from '../models/ApiResponseOfResendEmailConfirmationResponse';
import type { ApiResponseOfResetPasswordResponse } from '../models/ApiResponseOfResetPasswordResponse';
import type { ApiResponseOfResetPasswordWithCodeResponse } from '../models/ApiResponseOfResetPasswordWithCodeResponse';
import type { ApiResponseOfVerifyEmailWithCodeResponse } from '../models/ApiResponseOfVerifyEmailWithCodeResponse';
import type { CancelDeactivationCommand } from '../models/CancelDeactivationCommand';
import type { ChangePasswordCommand } from '../models/ChangePasswordCommand';
import type { DeactivateAccountCommand } from '../models/DeactivateAccountCommand';
import type { ForgotPasswordCommand } from '../models/ForgotPasswordCommand';
import type { LoginCommand } from '../models/LoginCommand';
import type { ModelsApiResponse } from '../models/ModelsApiResponse';
import type { RefreshTokenCommand } from '../models/RefreshTokenCommand';
import type { RegisterUserCommand } from '../models/RegisterUserCommand';
import type { ResendEmailConfirmationCommand } from '../models/ResendEmailConfirmationCommand';
import type { ResetPasswordCommand } from '../models/ResetPasswordCommand';
import type { ResetPasswordWithCodeCommand } from '../models/ResetPasswordWithCodeCommand';
import type { V1LogoutRequest } from '../models/V1LogoutRequest';
import type { VerifyEmailWithCodeCommand } from '../models/VerifyEmailWithCodeCommand';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AccountsApiService {
    /**
     * 用户注册 - 创建新用户账户
     * @param requestBody 注册信息
     * @returns ApiResponseOfRegisterUserResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiRegister(
        requestBody?: RegisterUserCommand,
    ): CancelablePromise<ApiResponseOfRegisterUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/register',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 用户登录 - 获取访问令牌
     * @param requestBody 登录信息
     * @returns ApiResponseOfLoginResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiLogin(
        requestBody?: LoginCommand,
    ): CancelablePromise<ApiResponseOfLoginResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/login',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 刷新访问令牌
     * @param requestBody 刷新令牌信息
     * @returns ApiResponseOfRefreshTokenResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiRefreshToken(
        requestBody?: RefreshTokenCommand,
    ): CancelablePromise<ApiResponseOfRefreshTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/refresh-token',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 登出（销毁刷新令牌）
     * @param requestBody
     * @returns ModelsApiResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiLogout(
        requestBody?: V1LogoutRequest,
    ): CancelablePromise<ModelsApiResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/logout',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 确认邮箱
     * @param userId 用户ID
     * @param token 确认令牌
     * @param expires 过期时间
     * @returns ApiResponseOfConfirmEmailResponse OK
     * @throws ApiError
     */
    public static getApiV1AccountsApiConfirmEmail(
        userId?: string,
        token?: string,
        expires?: string,
    ): CancelablePromise<ApiResponseOfConfirmEmailResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/AccountsApi/confirm-email',
            query: {
                'userId': userId,
                'token': token,
                'expires': expires,
            },
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 重新发送邮箱确认邮件
     * @param requestBody 重新发送邮箱确认邮件命令
     * @returns ApiResponseOfResendEmailConfirmationResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiResendEmailConfirmation(
        requestBody?: ResendEmailConfirmationCommand,
    ): CancelablePromise<ApiResponseOfResendEmailConfirmationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/resend-email-confirmation',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 使用验证码验证邮箱
     * @param requestBody 验证码验证命令
     * @returns ApiResponseOfVerifyEmailWithCodeResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiVerifyEmailWithCode(
        requestBody?: VerifyEmailWithCodeCommand,
    ): CancelablePromise<ApiResponseOfVerifyEmailWithCodeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/verify-email-with-code',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 修改密码
     * @param requestBody 修改密码命令
     * @returns ApiResponseOfChangePasswordResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiChangePassword(
        requestBody?: ChangePasswordCommand,
    ): CancelablePromise<ApiResponseOfChangePasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/change-password',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 忘记密码 - 发送密码重置邮件
     * @param requestBody 忘记密码命令
     * @returns ApiResponseOfForgotPasswordResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiForgotPassword(
        requestBody?: ForgotPasswordCommand,
    ): CancelablePromise<ApiResponseOfForgotPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/forgot-password',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 重置密码 - 使用重置令牌
     * @param requestBody 重置密码命令
     * @returns ApiResponseOfResetPasswordResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiResetPassword(
        requestBody?: ResetPasswordCommand,
    ): CancelablePromise<ApiResponseOfResetPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/reset-password',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 使用验证码重置密码 - 更简单的重置方式
     * @param requestBody 使用验证码重置密码命令
     * @returns ApiResponseOfResetPasswordWithCodeResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiResetPasswordWithCode(
        requestBody?: ResetPasswordWithCodeCommand,
    ): CancelablePromise<ApiResponseOfResetPasswordWithCodeResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/reset-password-with-code',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 用户注销账户（停用账户）
     * @param requestBody 注销账户命令
     * @returns ApiResponseOfDeactivateAccountResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiDeactivate(
        requestBody?: DeactivateAccountCommand,
    ): CancelablePromise<ApiResponseOfDeactivateAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/deactivate',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 取消账户注销
     * @param requestBody 取消注销命令
     * @returns ApiResponseOfCancelDeactivationResponse OK
     * @throws ApiError
     */
    public static postApiV1AccountsApiCancelDeactivation(
        requestBody?: CancelDeactivationCommand,
    ): CancelablePromise<ApiResponseOfCancelDeactivationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/AccountsApi/cancel-deactivation',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
}

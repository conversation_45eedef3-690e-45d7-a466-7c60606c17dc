/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ApiResponseOfCreateUserResponse } from '../models/ApiResponseOfCreateUserResponse';
import type { ApiResponseOfGetUserResponse } from '../models/ApiResponseOfGetUserResponse';
import type { ApiResponseOfPaginatedListOfGetUserResponse } from '../models/ApiResponseOfPaginatedListOfGetUserResponse';
import type { ApiResponseOfSuspendUserResponse } from '../models/ApiResponseOfSuspendUserResponse';
import type { ApiResponseOfUpdateUserResponse } from '../models/ApiResponseOfUpdateUserResponse';
import type { ApiResponseOfUpdateUserStatusResponse } from '../models/ApiResponseOfUpdateUserStatusResponse';
import type { CreateUserCommand } from '../models/CreateUserCommand';
import type { ModelsApiResponse } from '../models/ModelsApiResponse';
import type { SuspendUserCommand } from '../models/SuspendUserCommand';
import type { UpdateUserCommand } from '../models/UpdateUserCommand';
import type { UpdateUserStatusCommand } from '../models/UpdateUserStatusCommand';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class UsersApiService {
    /**
     * 获取当前用户信息
     * @returns ApiResponseOfGetUserResponse OK
     * @throws ApiError
     */
    public static getApiV1UsersApiMe(): CancelablePromise<ApiResponseOfGetUserResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/UsersApi/me',
            errors: {
                401: `Unauthorized`,
                403: `Forbidden`,
                404: `Not Found`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 更新当前用户信息
     * @param requestBody
     * @returns ApiResponseOfUpdateUserResponse OK
     * @throws ApiError
     */
    public static putApiV1UsersApiMe(
        requestBody?: UpdateUserCommand,
    ): CancelablePromise<ApiResponseOfUpdateUserResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/UsersApi/me',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 获取指定用户信息
     * @param id
     * @returns ApiResponseOfGetUserResponse OK
     * @throws ApiError
     */
    public static getApiV1UsersApi(
        id: string,
    ): CancelablePromise<ApiResponseOfGetUserResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/UsersApi/{id}',
            path: {
                'id': id,
            },
            errors: {
                401: `Unauthorized`,
                403: `Forbidden`,
                404: `Not Found`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 更新指定用户信息
     * @param id
     * @param requestBody
     * @returns ApiResponseOfUpdateUserResponse OK
     * @throws ApiError
     */
    public static putApiV1UsersApi(
        id: string,
        requestBody?: UpdateUserCommand,
    ): CancelablePromise<ApiResponseOfUpdateUserResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/UsersApi/{id}',
            path: {
                'id': id,
            },
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 删除用户
     * @param id
     * @returns ModelsApiResponse OK
     * @throws ApiError
     */
    public static deleteApiV1UsersApi(
        id: string,
    ): CancelablePromise<ModelsApiResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/UsersApi/{id}',
            path: {
                'id': id,
            },
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                404: `Not Found`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 获取用户列表
     * @param keyword
     * @param status
     * @param startDate
     * @param endDate
     * @param sortField
     * @param sortOrder
     * @param page
     * @param pageSize
     * @returns ApiResponseOfPaginatedListOfGetUserResponse OK
     * @throws ApiError
     */
    public static getApiV1UsersApi1(
        keyword?: string,
        status?: string,
        startDate?: string,
        endDate?: string,
        sortField?: string,
        sortOrder?: string,
        page: number = 1,
        pageSize: number = 10,
    ): CancelablePromise<ApiResponseOfPaginatedListOfGetUserResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/UsersApi',
            query: {
                'keyword': keyword,
                'status': status,
                'startDate': startDate,
                'endDate': endDate,
                'sortField': sortField,
                'sortOrder': sortOrder,
                'page': page,
                'pageSize': pageSize,
            },
            errors: {
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 创建用户
     * @param requestBody
     * @returns ApiResponseOfCreateUserResponse OK
     * @throws ApiError
     */
    public static postApiV1UsersApi(
        requestBody?: CreateUserCommand,
    ): CancelablePromise<ApiResponseOfCreateUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/UsersApi',
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 更新用户状态
     * @param id
     * @param requestBody
     * @returns ApiResponseOfUpdateUserStatusResponse OK
     * @throws ApiError
     */
    public static patchApiV1UsersApiStatus(
        id: string,
        requestBody?: UpdateUserStatusCommand,
    ): CancelablePromise<ApiResponseOfUpdateUserStatusResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/UsersApi/{id}/status',
            path: {
                'id': id,
            },
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                404: `Not Found`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 暂停用户
     * @param id
     * @param requestBody
     * @returns ApiResponseOfSuspendUserResponse OK
     * @throws ApiError
     */
    public static patchApiV1UsersApiSuspend(
        id: string,
        requestBody?: SuspendUserCommand,
    ): CancelablePromise<ApiResponseOfSuspendUserResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/UsersApi/{id}/suspend',
            path: {
                'id': id,
            },
            body: requestBody,
            mediaType: 'application/json; api-version=1.0',
            errors: {
                400: `Bad Request`,
                401: `Unauthorized`,
                403: `Forbidden`,
                404: `Not Found`,
                500: `Internal Server Error`,
            },
        });
    }
    /**
     * 获取系统用户列表
     * @param keyword
     * @param status
     * @param startDate
     * @param endDate
     * @param sortField
     * @param sortOrder
     * @param roles
     * @param page
     * @param pageSize
     * @returns ApiResponseOfPaginatedListOfGetUserResponse OK
     * @throws ApiError
     */
    public static getApiV1UsersApiSystem(
        keyword?: string,
        status?: string,
        startDate?: string,
        endDate?: string,
        sortField?: string,
        sortOrder?: string,
        roles?: Array<string>,
        page: number = 1,
        pageSize: number = 10,
    ): CancelablePromise<ApiResponseOfPaginatedListOfGetUserResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/UsersApi/system',
            query: {
                'keyword': keyword,
                'status': status,
                'startDate': startDate,
                'endDate': endDate,
                'sortField': sortField,
                'sortOrder': sortOrder,
                'roles': roles,
                'page': page,
                'pageSize': pageSize,
            },
            errors: {
                401: `Unauthorized`,
                403: `Forbidden`,
                500: `Internal Server Error`,
            },
        });
    }
}

/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelDeactivationResponse } from './CancelDeactivationResponse';
export type ApiResponseOfCancelDeactivationResponse = {
    success?: boolean;
    code?: number;
    message?: string | null;
    data?: CancelDeactivationResponse;
    errors?: Array<string> | null;
};


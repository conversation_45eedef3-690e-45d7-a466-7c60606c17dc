/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type GetUserResponse = {
    id?: string | null;
    userName?: string | null;
    email?: string | null;
    phoneNumber?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    avatarUrl?: string | null;
    createdAt?: string;
    lastLoginAt?: string | null;
    roles?: Array<string> | null;
    isActive?: boolean;
    isEmailConfirmed?: boolean;
    isPhoneConfirmed?: boolean;
    status?: string | null;
    isSystemUser?: boolean;
    deletedAt?: string | null;
};


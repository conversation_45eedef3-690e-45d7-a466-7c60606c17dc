/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { DeactivateAccountResponse } from './DeactivateAccountResponse';
export type ApiResponseOfDeactivateAccountResponse = {
    success?: boolean;
    code?: number;
    message?: string | null;
    data?: DeactivateAccountResponse;
    errors?: Array<string> | null;
};

